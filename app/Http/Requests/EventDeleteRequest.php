<?php

namespace App\Http\Requests;

/**
 * @property string $password パスワード
 */
class EventDeleteRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => ['required', 'string'],
        ];
    }

    public function attributes()
    {
        return [
            'password' => 'パスワード'
        ];
    }
}
