<?php

namespace App\Http\Middleware;

use App\Model\Event;
use App\Model\EventParticipant;
use App\Services\UserInfo;
use Closure;
use Illuminate\Http\Request;

class CheckEventJoined
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $eventId = $request->route('eventId');

        // イベントが存在するかチェック（論理削除されたものも含む）
        $event = Event::withTrashed()->find($eventId);
        if (!$event) {
            abort(404, "Event with ID {$eventId} not found");
        }

        // イベントが論理削除されている場合は404を返す
        if ($event->trashed()) {
            abort(404, "Event with ID {$eventId} has been deleted");
        }

        $userInfo = UserInfo::getOrFail($request);
        if(!EventParticipant::whereUserId($userInfo->user_id)->whereEventId($eventId)->withActiveEvents()->exists()) {
            EventParticipant::whereUserId($userInfo->user_id)->withActiveEvents()->where(function($query) use ($request){
                $query->where('event_id', $request->input('eventId'))
                    ->orWhere('event_id', $request->input('event_id'));
            })->firstOrFail();
        }

        return $next($request);
    }
}
